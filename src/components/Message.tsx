import { ThemedStyle } from "@/theme"
import React, { useState, useCallback } from "react"
import { memo } from "react"
import { View, Text, StyleSheet, Animated, Easing, ViewStyle, TouchableOpacity } from "react-native"
import Markdown from "react-native-marked"
import { useAppTheme } from "@/utils/useAppTheme"
import { ChatErrorBoundary } from "./ErrorBoundary/ChatErrorBoundary"
import { createMessageRenderError, ErrorType } from "@/types/errors"
import { Button } from "./Button"
import { Icon } from "./Icon"

type MessageProps = {
  role: "user" | "assistant"
  content: string
  isComplete?: boolean
  onRetry?: () => void
}

/**
 * Safe markdown renderer with error handling
 */
const SafeMarkdownRenderer = memo(
  ({ content, onRetry }: { content: string; onRetry?: () => void }) => {
    const { themed } = useAppTheme()
    const [renderError, setRenderError] = useState<Error | null>(null)

    const renderMarkdown = useCallback(() => {
      try {
        setRenderError(null)
        return <Markdown value={content} />
      } catch (error) {
        const markdownError = createMessageRenderError(error as Error)
        setRenderError(markdownError)
        return null
      }
    }, [content])

    const handleRetry = useCallback(() => {
      setRenderError(null)
      onRetry?.()
    }, [onRetry])

    if (renderError) {
      return (
        <View style={themed($errorContainer)}>
          <View style={themed($errorContent)}>
            <Icon icon="alertCircle" size={16} style={themed($errorIcon)} />
            <Text style={themed($errorText)}>Failed to render message</Text>
          </View>
          <TouchableOpacity style={themed($retryButton)} onPress={handleRetry}>
            <Text style={themed($retryText)}>Retry</Text>
          </TouchableOpacity>
          <Text style={themed($fallbackText)}>{content}</Text>
        </View>
      )
    }

    return renderMarkdown()
  },
)

const Message = memo(({ role, content, isComplete = true, onRetry }: MessageProps) => {
  const { themed } = useAppTheme()
  const pulseAnim = React.useRef(new Animated.Value(0)).current

  // Animation for the typing indicator
  React.useEffect(() => {
    if (!isComplete) {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
            easing: Easing.inOut(Easing.ease),
          }),
          Animated.timing(pulseAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
            easing: Easing.inOut(Easing.ease),
          }),
        ]),
      )
      pulse.start()
      return () => pulse.stop()
    }
    return undefined // Explicitly return undefined when isComplete is true
  }, [isComplete, pulseAnim])

  // Interpolate the animation value for the typing indicator
  const opacity = pulseAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.5, 1],
  })

  return (
    <ChatErrorBoundary>
      <View
        style={themed([
          $messageContainer,
          role === "assistant" ? $assistantContainer : $userContainer,
        ])}
      >
        <View
          style={themed([$messageContent, role === "assistant" ? $assistantContent : $userContent])}
        >
          <SafeMarkdownRenderer content={content} onRetry={onRetry} />
          {!isComplete && <Animated.View style={[themed($typingIndicator), { opacity }]} />}
        </View>
      </View>
    </ChatErrorBoundary>
  )
})

const $messageContainer: ThemedStyle<ViewStyle> = (theme) => ({
  width: "100%",
  flexDirection: "row",
  marginTop: theme.spacing.xs,
})

const $assistantContainer: ThemedStyle<ViewStyle> = (theme) => ({
  justifyContent: "flex-start",
})

const $userContainer: ThemedStyle<ViewStyle> = (theme) => ({
  justifyContent: "flex-end",
})

const $messageContent: ThemedStyle<ViewStyle> = (theme) => ({
  borderRadius: theme.spacing.xs,
  padding: theme.spacing.xs,
  maxWidth: "80%",
})

const $assistantContent: ThemedStyle<ViewStyle> = (theme) => ({
  backgroundColor: theme.colors.palette.neutral500,
  borderRadius: theme.spacing.xs,
  alignSelf: "flex-start",
})

const $userContent: ThemedStyle<ViewStyle> = (theme) => ({
  backgroundColor: theme.colors.palette.primary500,
  alignSelf: "flex-end",
  marginHorizontal: theme.spacing.sm,
})

const $typingIndicator: ThemedStyle<ViewStyle> = (theme) => ({
  width: 8,
  height: 16,
  backgroundColor: theme.colors.palette.neutral900,
  marginTop: 4,
})

const $errorContainer: ThemedStyle<ViewStyle> = (theme) => ({
  padding: theme.spacing.sm,
  backgroundColor: theme.colors.errorBackground,
  borderRadius: theme.spacing.xs,
  borderWidth: 1,
  borderColor: theme.colors.error,
})

const $errorContent: ThemedStyle<ViewStyle> = (theme) => ({
  flexDirection: "row",
  alignItems: "center",
  marginBottom: theme.spacing.xs,
})

const $errorIcon: ThemedStyle<ViewStyle> = (theme) => ({
  marginRight: theme.spacing.xs,
  tintColor: theme.colors.error,
})

const $errorText: ThemedStyle<ViewStyle> = (theme) => ({
  color: theme.colors.error,
  fontSize: 14,
  fontWeight: "500",
})

const $retryButton: ThemedStyle<ViewStyle> = (theme) => ({
  backgroundColor: theme.colors.palette.primary500,
  paddingHorizontal: theme.spacing.sm,
  paddingVertical: theme.spacing.xs,
  borderRadius: theme.spacing.xs,
  alignSelf: "flex-start",
  marginBottom: theme.spacing.xs,
})

const $retryText: ThemedStyle<ViewStyle> = (theme) => ({
  color: theme.colors.palette.neutral100,
  fontSize: 12,
  fontWeight: "600",
})

const $fallbackText: ThemedStyle<ViewStyle> = (theme) => ({
  color: theme.colors.textDim,
  fontSize: 14,
  fontStyle: "italic",
})

export { Message }
