import { createOpenRouter } from "@openrouter/ai-sdk-provider"
import { streamText } from "ai"
import { z } from "zod"
import { FREE_MODELS } from "@/services/models"

const ModelNameSchema = z
  .string()
  .transform((name) => (FREE_MODELS.includes(name as any) ? name : FREE_MODELS[0]))

// Request payload schema – aligns with `@vercel/ai` & `useChat` expectations
//   { messages: ChatMessage[]; modelName?: string }
const ChatRequestSchema = z.object({
  messages: z.array(
    z.object({
      role: z.enum(["user", "assistant", "system", "function"]).optional(),
      content: z.string(),
    }),
  ),
  modelName: ModelNameSchema.default(FREE_MODELS[0]),
})

// Helper: stream model answer from OpenRouter. Kept generic so can be reused.
export const askModelStream = (
  modelName: string,
  messages: { role?: string; content: string }[],
) => {
  const openrouter = createOpenRouter({
    apiKey: process.env.OPENROUTER_API_KEY,
  })
  return streamText({
    model: openrouter(modelName),
    messages: messages as any,
  })
}

export async function POST(req: Request) {
  try {
    // Parse and validate request body
    let body
    try {
      body = await req.json()
    } catch (error) {
      return new Response(
        JSON.stringify({
          error: "Invalid JSON in request body",
          code: "INVALID_JSON",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      )
    }

    // Validate & coerce request according to our schema
    let validatedData
    try {
      validatedData = ChatRequestSchema.parse(body)
    } catch (error) {
      return new Response(
        JSON.stringify({
          error: "Invalid request format",
          code: "VALIDATION_ERROR",
          details: error instanceof Error ? error.message : "Unknown validation error",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      )
    }

    const { messages, modelName } = validatedData

    // Additional validation
    if (!messages || messages.length === 0) {
      return new Response(
        JSON.stringify({
          error: "No messages provided",
          code: "EMPTY_MESSAGES",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      )
    }

    // Check for rate limiting (basic implementation)
    const userAgent = req.headers.get("user-agent") || "unknown"
    // In a real app, you'd implement proper rate limiting here

    try {
      const result = askModelStream(modelName, messages)

      // Return as an octet-stream, same headers the Vercel AI SDK example uses
      return result.toDataStreamResponse({
        headers: {
          "Content-Type": "application/octet-stream",
          "Content-Encoding": "none",
        },
      })
    } catch (aiError) {
      console.error("AI service error:", aiError)

      // Handle specific AI service errors
      const errorMessage = aiError instanceof Error ? aiError.message : "Unknown AI service error"

      if (errorMessage.includes("rate limit")) {
        return new Response(
          JSON.stringify({
            error: "Rate limit exceeded. Please try again later.",
            code: "RATE_LIMIT_EXCEEDED",
            retryAfter: 60,
          }),
          {
            status: 429,
            headers: {
              "Content-Type": "application/json",
              "Retry-After": "60",
            },
          },
        )
      }

      if (errorMessage.includes("unauthorized") || errorMessage.includes("api key")) {
        return new Response(
          JSON.stringify({
            error: "AI service authentication failed",
            code: "AI_AUTH_ERROR",
          }),
          {
            status: 503,
            headers: { "Content-Type": "application/json" },
          },
        )
      }

      // Generic AI service error
      return new Response(
        JSON.stringify({
          error: "AI service temporarily unavailable",
          code: "AI_SERVICE_ERROR",
        }),
        {
          status: 503,
          headers: { "Content-Type": "application/json" },
        },
      )
    }
  } catch (error) {
    console.error("Unexpected error in chat API:", error)

    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      },
    )
  }
}

export const askModel = async (modelName: string, content: string) => {
  const input = ChatRequestSchema.parse({ messages: [{ role: "user", content }], modelName })

  const openrouter = createOpenRouter({
    apiKey: process.env.OPENROUTER_API_KEY,
  })

  const response = streamText({
    model: openrouter(modelName),
    prompt:
      "You are a concise and up-to-date information provider. When answering questions, prioritize accuracy and brevity. Be aware of current events and recent developments in various fields. Prompt: " +
      content,
    maxTokens: 100,
    temperature: 0.2,
    topP: 0.9,
    frequencyPenalty: 0,
    presencePenalty: 0,
    stopSequences: ["\n"],
  })

  await response.consumeStream()
  const result = response.text
  return result
}
