import React, { useState, useEffect } from "react"
import { TouchableOpacity, Text, View } from "react-native"
import { Drawer } from "expo-router/drawer"
import CustomDrawer from "@/components/CustomDrawer"
import { LoadingScreen } from "@/components/LoadingScreen"
import { ChatErrorBoundary } from "@/components/ErrorBoundary/ChatErrorBoundary"
import { useRouter } from "expo-router"
import { useQuery } from "convex/react"
import { useConvexAuth } from "convex/react"
import { api } from "convex/_generated/api"
import { Redirect } from "expo-router"

export default function ChatScreen() {
  const router = useRouter()

  const { isAuthenticated, isLoading } = useConvexAuth()
  const [searchQuery, setSearchQuery] = useState("")
  const [loadingTimeout, setLoadingTimeout] = useState(false)

  // Debug logging for authentication state
  useEffect(() => {
    console.log("🔐 Auth State:", { isAuthenticated, isLoading })

    // Check environment variables (client-side only)
    console.log("🌍 Environment Check:", {
      convexUrl: !!process.env.EXPO_PUBLIC_CONVEX_URL,
      clerkKey: !!process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY,
      convexUrlValue: process.env.EXPO_PUBLIC_CONVEX_URL?.substring(0, 20) + "...",
    })

    // Log more detailed auth state
    console.log("🔍 Detailed Auth Debug:", {
      timestamp: new Date().toISOString(),
      isAuthenticated,
      isLoading,
    })

    // Set a timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      if (isLoading) {
        console.warn("⚠️ Authentication loading timeout - forcing continue")
        console.warn("This might indicate a Convex + Clerk integration issue")
        console.warn("Possible causes:")
        console.warn("1. Convex auth.config.ts domain mismatch")
        console.warn("2. Clerk token validation failing")
        console.warn("3. Network connectivity issues")
        setLoadingTimeout(true)
      }
    }, 5000) // Reduced to 5 seconds for faster debugging

    return () => clearTimeout(timeout)
  }, [isAuthenticated, isLoading])

  // Always call hooks in the same order. Gate the query with "skip" when the user is
  // not authenticated so the hook call is still executed after sign-out.
  const filteredThreads = useQuery(
    api.chat.searchThreadsByTitle,
    isAuthenticated ? { searchQuery } : "skip",
  )

  // Show loading screen while authentication state is being determined
  // But not if we've hit the timeout
  if (isLoading && !loadingTimeout) {
    return <LoadingScreen message="Checking authentication..." />
  }

  // TEMPORARY: If we hit the timeout, let's bypass authentication to test the app
  if (loadingTimeout) {
    console.warn("🚨 BYPASSING AUTHENTICATION DUE TO TIMEOUT - FOR DEBUGGING ONLY")
    // Continue to render the app even though auth is stuck
  }

  // If we hit the timeout, show an error message with retry option
  if (loadingTimeout && isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center", padding: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: "bold", marginBottom: 10, textAlign: "center" }}>
          Connection Issue
        </Text>
        <Text style={{ fontSize: 14, textAlign: "center", marginBottom: 20 }}>
          Unable to connect to the authentication service. This might be a network issue or a
          problem with the Convex backend.
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor: "#007AFF",
            padding: 15,
            borderRadius: 8,
            minWidth: 120,
            alignItems: "center",
            marginBottom: 10,
          }}
          onPress={() => {
            setLoadingTimeout(false)
            // Force a re-render to retry authentication
            window.location?.reload?.() // For web
          }}
        >
          <Text style={{ color: "white", fontWeight: "600" }}>Retry</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            backgroundColor: "#666",
            padding: 15,
            borderRadius: 8,
            minWidth: 120,
            alignItems: "center",
          }}
          onPress={() => {
            setLoadingTimeout(false)
            // Continue without authentication (will redirect to sign-in)
          }}
        >
          <Text style={{ color: "white", fontWeight: "600" }}>Continue</Text>
        </TouchableOpacity>
      </View>
    )
  }

  // Redirect unauthenticated users after hooks have been called.
  // BUT: If we hit the timeout, let's continue to see if the app works
  if (!isAuthenticated && !loadingTimeout) {
    return <Redirect href={"/sign-in"} />
  }

  const handleLogin = () => {
    router.push("/sign-in")
  }

  return (
    <ChatErrorBoundary>
      <Drawer
        screenOptions={{
          headerShown: true,
        }}
        drawerContent={(props) => (
          <CustomDrawer
            {...props}
            chatThreads={filteredThreads}
            onLogin={handleLogin}
            onSearchChange={setSearchQuery}
            searchQuery={searchQuery}
          />
        )}
      >
        <Drawer.Screen
          name="index"
          options={{
            drawerLabel: "Create New Chat",
            title: "Create New Chat",
          }}
        />
        <Drawer.Screen
          name="[threadId]"
          options={{
            drawerLabel: "Chat Thread",
            title: "Chat Thread",
            headerRight: () => (
              <TouchableOpacity
                style={{
                  backgroundColor: "#D97D54",
                  borderRadius: 8,
                  paddingHorizontal: 12,
                  paddingVertical: 6,
                  marginRight: 16,
                }}
                onPress={() => router.push("/")}
              >
                <Text style={{ color: "#fff", fontWeight: "600" }}>New Chat</Text>
              </TouchableOpacity>
            ),
          }}
        />
      </Drawer>
    </ChatErrorBoundary>
  )
}
