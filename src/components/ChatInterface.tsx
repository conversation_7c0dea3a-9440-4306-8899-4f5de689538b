import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  Pressable,
  Alert,
} from "react-native"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { useAppTheme } from "@/utils/useAppTheme"
import { observer } from "mobx-react-lite"
import { useState, useCallback, useRef } from "react"
import {
  $modalOverlay,
  $errorContainer,
  $errorContent,
  $errorText,
  $retryButton,
  $retryButtonText,
  $dismissButton,
  $dismissButtonText,
  $debugErrorText,
} from "@/styles/common"
import { ChatErrorBoundary } from "./ErrorBoundary/ChatErrorBoundary"
import {
  validateMessageInput,
  messageRateLimiter,
  sanitizeInput,
  isSpamInput,
} from "@/utils/validation"
import {
  ChatError,
  ErrorType,
  createNetworkError,
  createTimeoutError,
  createRateLimitError,
  createAuthError,
  createAIServiceError,
  isRetryableError,
  getUserFriendlyMessage,
} from "@/types/errors"
import {
  $chatContainer,
  $chatTopContainer,
  $modelSelector,
  $modelPicker,
  $messageContainer,
  $roleText,
  $messageText,
  $bottomContainer,
  $pickerText,
  $pickerModal,
  $pickerOption,
  $pickerOptionText,
  $chatInput,
  $messagesScroll,
  $inputContainer,
  $sendButton,
  $sendButtonText,
  $sendButtonDisabled,
  $chatInputError,
  $inputErrorText,
  $newChatButton,
  $newChatButtonText,
} from "@/styles/chat"
import { KeyboardAvoidingView } from "react-native-keyboard-controller"
import { router, useLocalSearchParams } from "expo-router"
import { useMutation, useAction, useConvexAuth, useQuery } from "convex/react"
import { api } from "convex/_generated/api"
import { Message } from "./Message"
import { Id } from "convex/_generated/dataModel"
import { FREE_MODELS } from "@/services/models"

interface ChatInterfaceProps {
  apiEndpoint?: string
}

export const ChatInterface = observer(function ChatInterface({ apiEndpoint }: ChatInterfaceProps) {
  const { threadId } = useLocalSearchParams<{ threadId?: Id<"threads"> | undefined }>()
  let parsedThreadId = threadId ? threadId : undefined
  const [selectedModel, setSelectedModel] = useState(FREE_MODELS[0])
  const [isModelPickerVisible, setIsModelPickerVisible] = useState(false)

  const { theme, themed } = useAppTheme()
  const insets = useSafeAreaInsetsStyle(["bottom"])

  const [isSending, setIsSending] = useState(false)
  const [input, setInput] = useState("")
  const [error, setError] = useState<ChatError | null>(null)
  const [inputError, setInputError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [isRetrying, setIsRetrying] = useState(false)
  const { isAuthenticated } = useConvexAuth()

  // Refs for managing timeouts and retries
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const maxRetries = 3
  const retryDelay = 1000 // Start with 1 second

  const startChat = useAction(api.chat.startChatMessagePair)
  const createUserThread = useMutation(api.chat.createUserThread)

  // Enhanced error handling for API calls
  const handleApiError = useCallback((error: unknown): ChatError => {
    if (error instanceof ChatError) {
      return error
    }

    const errorMessage = error instanceof Error ? error.message : String(error)

    // Check for specific error types
    if (errorMessage.includes("Unauthorized") || errorMessage.includes("sign in")) {
      return createAuthError(errorMessage)
    }

    if (errorMessage.includes("network") || errorMessage.includes("fetch")) {
      return createNetworkError(error as Error)
    }

    if (errorMessage.includes("timeout")) {
      return createTimeoutError()
    }

    if (errorMessage.includes("rate limit") || errorMessage.includes("too many requests")) {
      return createRateLimitError()
    }

    // Default to AI service error
    return createAIServiceError(error as Error)
  }, [])

  const sendMessage = useCallback(async () => {
    // Validate input
    const validation = validateMessageInput(input)
    if (!validation.isValid) {
      setError(validation.error as ChatError)
      return
    }

    // Check for spam
    if (isSpamInput(input)) {
      setError(
        new ChatError(ErrorType.VALIDATION_ERROR, "Message appears to be spam", {
          userMessage: "Please enter a meaningful message.",
        }),
      )
      return
    }

    // Check rate limiting
    if (!messageRateLimiter.isAllowed()) {
      const timeUntilReset = messageRateLimiter.getTimeUntilReset()
      setError(createRateLimitError(Math.ceil(timeUntilReset / 1000)))
      return
    }

    const sanitizedInput = sanitizeInput(validation.sanitizedValue!)

    try {
      setIsSending(true)
      setError(null)

      let currentThreadId = parsedThreadId
      if (!currentThreadId) {
        currentThreadId = await createUserThread({
          error: undefined,
        })
      }

      const { threadId } = await startChat({
        threadId: currentThreadId,
        content: sanitizedInput,
        model: selectedModel,
      })

      setInput("")
      setError(null)

      if (threadId && threadId !== parsedThreadId) {
        router.replace(`/${threadId}`)
      }
    } catch (err) {
      const chatError = handleApiError(err)
      setError(chatError)
    } finally {
      setIsSending(false)
    }
  }, [input, parsedThreadId, selectedModel, createUserThread, startChat, handleApiError])

  const handleSubmit = useCallback(async () => {
    await sendMessage()
  }, [sendMessage])

  const handleRetry = useCallback(() => {
    setError(null)
    setRetryCount(0)
    sendMessage()
  }, [sendMessage])

  const clearError = useCallback(() => {
    setError(null)
    setRetryCount(0)
  }, [])

  // Real-time input validation
  const handleInputChange = useCallback(
    (text: string) => {
      setInput(text)
      setInputError(null)

      // Clear any existing errors when user starts typing
      if (error) {
        setError(null)
      }

      // Real-time validation
      if (text.length > 0) {
        const validation = validateMessageInput(text)
        if (!validation.isValid && validation.error) {
          setInputError(validation.error.userMessage || validation.error.message)
        }
      }
    },
    [error],
  )

  const messages = useQuery(
    api.messages.getMessages,
    parsedThreadId ? { threadId: parsedThreadId, limit: 100 } : "skip",
  )

  return (
    <ChatErrorBoundary>
      <KeyboardAvoidingView style={[themed($chatContainer), insets]}>
        <View style={themed($chatTopContainer)}>
          <View style={themed($modelSelector)}>
            <TouchableOpacity
              style={themed($modelPicker)}
              onPress={() => setIsModelPickerVisible(true)}
            >
              <Text style={themed($pickerText)}>{selectedModel}</Text>
            </TouchableOpacity>
            <Modal
              visible={isModelPickerVisible}
              transparent
              animationType="slide"
              onRequestClose={() => setIsModelPickerVisible(false)}
            >
              <TouchableOpacity
                style={themed($modalOverlay)}
                onPress={() => setIsModelPickerVisible(false)}
                activeOpacity={1}
              >
                <View style={themed($pickerModal)}>
                  {FREE_MODELS.map((model) => (
                    <TouchableOpacity
                      key={model}
                      style={themed($pickerOption)}
                      onPress={() => {
                        setSelectedModel(model)
                        setIsModelPickerVisible(false)
                      }}
                    >
                      <Text style={themed($pickerOptionText)}>{model}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </TouchableOpacity>
            </Modal>
          </View>
          <FlatList
            style={themed($messagesScroll)}
            data={messages ?? []}
            keyExtractor={(message) => message._id}
            renderItem={({ item: message }) => (
              <Message
                key={message._id}
                role={message.role}
                content={message.content}
                isComplete={message.isComplete}
              />
            )}
            contentContainerStyle={{ flexGrow: 1, justifyContent: "flex-start" }}
            showsVerticalScrollIndicator={true}
          />
        </View>
        <View style={themed($bottomContainer)}>
          {error && (
            <View style={themed($errorContainer)}>
              <View style={themed($errorContent)}>
                <Text style={themed($errorText)}>{getUserFriendlyMessage(error)}</Text>
                {isRetryableError(error) && (
                  <TouchableOpacity
                    style={themed($retryButton)}
                    onPress={handleRetry}
                    disabled={isSending}
                  >
                    <Text style={themed($retryButtonText)}>
                      {isSending ? "Retrying..." : "Retry"}
                    </Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity style={themed($dismissButton)} onPress={clearError}>
                  <Text style={themed($dismissButtonText)}>✕</Text>
                </TouchableOpacity>
              </View>
              {__DEV__ && <Text style={themed($debugErrorText)}>Debug: {error.message}</Text>}
            </View>
          )}
          <View style={themed($inputContainer)}>
            <View style={{ flex: 1 }}>
              <TextInput
                style={[themed($chatInput), { flex: 1 }, inputError && themed($chatInputError)]}
                placeholder="Ask me anything..."
                value={input}
                onChangeText={handleInputChange}
                onSubmitEditing={() => handleSubmit()}
                editable={!isSending}
                autoFocus={true}
                multiline
                maxLength={10000}
              />
              {inputError && <Text style={themed($inputErrorText)}>{inputError}</Text>}
            </View>
            <TouchableOpacity
              style={themed([$sendButton, (isSending || !input.trim()) && $sendButtonDisabled])}
              onPress={() => handleSubmit()}
              disabled={!input.trim() || isSending}
            >
              <Text style={themed($sendButtonText)}>{isSending ? "..." : "➤"}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </ChatErrorBoundary>
  )
})
