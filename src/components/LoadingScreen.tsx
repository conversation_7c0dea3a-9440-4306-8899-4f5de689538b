import React from "react";
import { View, ActivityIndicator, ViewStyle } from "react-native";
import { Text } from "./Text";
import { useAppTheme } from "@/utils/useAppTheme";
import { ThemedStyle } from "@/theme";

interface LoadingScreenProps {
  message?: string;
  size?: "small" | "large";
}

/**
 * A reusable loading screen component
 */
export function LoadingScreen({ 
  message = "Loading...", 
  size = "large" 
}: LoadingScreenProps) {
  const { themed } = useAppTheme();

  return (
    <View style={themed($container)}>
      <ActivityIndicator 
        size={size} 
        color={themed($spinner).color}
      />
      <Text style={themed($message)} text={message} />
    </View>
  );
}

const $container: ThemedStyle<ViewStyle> = (theme) => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: theme.colors.background,
  padding: theme.spacing.lg,
});

const $spinner: ThemedStyle<{ color: string }> = (theme) => ({
  color: theme.colors.palette.primary500,
});

const $message: ThemedStyle<ViewStyle> = (theme) => ({
  marginTop: theme.spacing.md,
  color: theme.colors.textDim,
  fontSize: 16,
  textAlign: "center",
});
