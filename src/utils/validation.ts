import { createValidationError, ErrorType } from "@/types/errors";

/**
 * Configuration for input validation
 */
export const VALIDATION_CONFIG = {
  MESSAGE: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 10000, // 10k characters
    MAX_LINES: 100,
  },
  THREAD_TITLE: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 100,
  },
} as const;

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  error?: Error;
  sanitizedValue?: string;
}

/**
 * Validates message input before sending to AI
 */
export function validateMessageInput(input: string): ValidationResult {
  // Check if input is empty or only whitespace
  const trimmedInput = input.trim();
  if (trimmedInput.length === 0) {
    return {
      isValid: false,
      error: createValidationError("message", "Message cannot be empty"),
    };
  }

  // Check minimum length
  if (trimmedInput.length < VALIDATION_CONFIG.MESSAGE.MIN_LENGTH) {
    return {
      isValid: false,
      error: createValidationError("message", "Message is too short"),
    };
  }

  // Check maximum length
  if (trimmedInput.length > VALIDATION_CONFIG.MESSAGE.MAX_LENGTH) {
    return {
      isValid: false,
      error: createValidationError(
        "message",
        `Message is too long. Maximum ${VALIDATION_CONFIG.MESSAGE.MAX_LENGTH} characters allowed.`
      ),
    };
  }

  // Check line count
  const lineCount = trimmedInput.split('\n').length;
  if (lineCount > VALIDATION_CONFIG.MESSAGE.MAX_LINES) {
    return {
      isValid: false,
      error: createValidationError(
        "message",
        `Message has too many lines. Maximum ${VALIDATION_CONFIG.MESSAGE.MAX_LINES} lines allowed.`
      ),
    };
  }

  // Basic content filtering
  const contentValidation = validateMessageContent(trimmedInput);
  if (!contentValidation.isValid) {
    return contentValidation;
  }

  return {
    isValid: true,
    sanitizedValue: trimmedInput,
  };
}

/**
 * Basic content filtering for inappropriate content
 */
function validateMessageContent(input: string): ValidationResult {
  // List of patterns to check for potentially harmful content
  const suspiciousPatterns = [
    // Potential injection attempts
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /data:text\/html/gi,
    
    // Potential prompt injection
    /ignore\s+previous\s+instructions/gi,
    /forget\s+everything/gi,
    /you\s+are\s+now/gi,
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(input)) {
      return {
        isValid: false,
        error: createValidationError(
          "message",
          "Message contains potentially harmful content and cannot be sent."
        ),
      };
    }
  }

  return { isValid: true };
}

/**
 * Validates thread title input
 */
export function validateThreadTitle(title: string): ValidationResult {
  const trimmedTitle = title.trim();
  
  if (trimmedTitle.length === 0) {
    return {
      isValid: false,
      error: createValidationError("title", "Title cannot be empty"),
    };
  }

  if (trimmedTitle.length > VALIDATION_CONFIG.THREAD_TITLE.MAX_LENGTH) {
    return {
      isValid: false,
      error: createValidationError(
        "title",
        `Title is too long. Maximum ${VALIDATION_CONFIG.THREAD_TITLE.MAX_LENGTH} characters allowed.`
      ),
    };
  }

  return {
    isValid: true,
    sanitizedValue: trimmedTitle,
  };
}

/**
 * Sanitizes user input by removing potentially harmful content
 */
export function sanitizeInput(input: string): string {
  return input
    .trim()
    // Remove null bytes
    .replace(/\0/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    // Remove excessive newlines
    .replace(/\n{3,}/g, '\n\n');
}

/**
 * Checks if input contains only whitespace or common spam patterns
 */
export function isSpamInput(input: string): boolean {
  const trimmedInput = input.trim();
  
  // Empty or only whitespace
  if (trimmedInput.length === 0) {
    return true;
  }
  
  // Repeated characters (more than 10 of the same character)
  if (/(.)\1{10,}/.test(trimmedInput)) {
    return true;
  }
  
  // Only special characters
  if (/^[^\w\s]+$/.test(trimmedInput)) {
    return true;
  }
  
  return false;
}

/**
 * Rate limiting validation
 */
export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
}

export class RateLimiter {
  private requests: number[] = [];
  
  constructor(private config: RateLimitConfig) {}
  
  isAllowed(): boolean {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    
    // Remove old requests outside the window
    this.requests = this.requests.filter(time => time > windowStart);
    
    // Check if we're under the limit
    if (this.requests.length >= this.config.maxRequests) {
      return false;
    }
    
    // Add current request
    this.requests.push(now);
    return true;
  }
  
  getTimeUntilReset(): number {
    if (this.requests.length === 0) {
      return 0;
    }
    
    const oldestRequest = Math.min(...this.requests);
    const resetTime = oldestRequest + this.config.windowMs;
    return Math.max(0, resetTime - Date.now());
  }
}

/**
 * Default rate limiter for message sending
 */
export const messageRateLimiter = new RateLimiter({
  maxRequests: 10, // 10 messages
  windowMs: 60 * 1000, // per minute
});
