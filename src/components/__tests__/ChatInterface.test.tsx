import React from "react"
import { render, screen, fireEvent, waitFor } from "@testing-library/react-native"
import { ChatInterface } from "../ChatInterface"
import { ThemeProvider } from "@react-navigation/native"
import { ConvexProvider } from "convex/react"
import { SafeAreaProvider } from "react-native-safe-area-context"
import { useThemeProvider } from "@/utils/useAppTheme"

// Mock native dependencies
jest.mock("react-native-keyboard-controller", () => ({
  KeyboardAvoidingView: ({ children, ...props }: any) => {
    const { View } = require("react-native")
    return <View {...props}>{children}</View>
  },
}))

jest.mock("react-native-safe-area-context", () => ({
  SafeAreaProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}))

// Mock dependencies
jest.mock("expo-router", () => ({
  useLocalSearchParams: () => ({ threadId: undefined }),
  router: {
    replace: jest.fn(),
  },
}))

jest.mock("convex/react", () => ({
  useConvexAuth: () => ({ isAuthenticated: true }),
  useQuery: () => [],
  useMutation: () => jest.fn(),
  useAction: () => jest.fn(),
  ConvexProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}))

jest.mock("@/utils/validation", () => ({
  validateMessageInput: (input: string) => ({
    isValid: input.trim().length > 0,
    sanitizedValue: input.trim(),
  }),
  messageRateLimiter: {
    isAllowed: () => true,
    getTimeUntilReset: () => 0,
  },
  sanitizeInput: (input: string) => input.trim(),
  isSpamInput: () => false,
}))

jest.mock("@/services/models", () => ({
  FREE_MODELS: ["gpt-3.5-turbo", "claude-3-haiku"],
}))

jest.mock("@/utils/useSafeAreaInsetsStyle", () => ({
  useSafeAreaInsetsStyle: () => ({}),
}))

jest.mock("../ErrorBoundary/ChatErrorBoundary", () => ({
  ChatErrorBoundary: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}))

jest.mock("../Message", () => ({
  Message: ({ role, content }: { role: string; content: string }) => {
    const { Text } = require("react-native")
    return <Text>{`${role}: ${content}`}</Text>
  },
}))

jest.mock("@/types/errors", () => ({
  ChatError: class ChatError extends Error {
    constructor(type: string, message: string, options?: any) {
      super(message)
      this.userMessage = options?.userMessage
    }
  },
  getUserFriendlyMessage: (error: Error) => error.message,
  isRetryableError: () => true,
}))

jest.mock("convex/_generated/api", () => ({
  api: {
    chat: {
      startChatMessagePair: "startChatMessagePair",
      createUserThread: "createUserThread",
    },
    messages: {
      getMessages: "getMessages",
    },
  },
}))

jest.mock("convex/_generated/dataModel", () => ({
  Id: String,
}))

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const { ThemeProvider: CustomThemeProvider, navigationTheme } = useThemeProvider()

  return (
    <SafeAreaProvider>
      <CustomThemeProvider value={{ themeScheme: "light", setThemeContextOverride: jest.fn() }}>
        <ThemeProvider value={navigationTheme}>
          <ConvexProvider client={{} as any}>{children}</ConvexProvider>
        </ThemeProvider>
      </CustomThemeProvider>
    </SafeAreaProvider>
  )
}

describe("ChatInterface", () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it("renders without infinite re-render loops", async () => {
    const renderSpy = jest.fn()

    const TestComponent = () => {
      renderSpy()
      return <ChatInterface />
    }

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>,
    )

    // Wait for initial render to complete
    await waitFor(() => {
      expect(screen.getByPlaceholderText("Ask me anything...")).toBeTruthy()
    })

    // Check that the component doesn't re-render excessively
    // Allow for a few initial renders but not infinite loops
    expect(renderSpy).toHaveBeenCalledTimes(1)
  })

  it("handles text input without causing re-renders", async () => {
    const renderSpy = jest.fn()

    const TestComponent = () => {
      renderSpy()
      return <ChatInterface />
    }

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>,
    )

    const textInput = screen.getByPlaceholderText("Ask me anything...")

    // Clear the render count after initial render
    renderSpy.mockClear()

    // Type in the input
    fireEvent.changeText(textInput, "Hello")

    // Wait for any potential re-renders
    await waitFor(() => {
      expect(textInput.props.value).toBe("Hello")
    })

    // Should not cause excessive re-renders
    expect(renderSpy).toHaveBeenCalledTimes(1)
  })

  it("handles model selection without causing re-renders", async () => {
    const renderSpy = jest.fn()

    const TestComponent = () => {
      renderSpy()
      return <ChatInterface />
    }

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>,
    )

    // Find and press the model picker
    const modelPicker = screen.getByText("gpt-3.5-turbo")

    // Clear the render count after initial render
    renderSpy.mockClear()

    fireEvent.press(modelPicker)

    // Wait for modal to appear
    await waitFor(() => {
      expect(screen.getByText("claude-3-haiku")).toBeTruthy()
    })

    // Should not cause excessive re-renders
    expect(renderSpy).toHaveBeenCalledTimes(1)
  })
})
