import React, { Component, ErrorInfo, ReactNode } from "react";
import { View, ViewStyle, TextStyle } from "react-native";
import { Text } from "../Text";
import { Button } from "../Button";
import { Icon } from "../Icon";
import { useAppTheme } from "@/utils/useAppTheme";
import { ThemedStyle } from "@/theme";
import { ErrorType, ChatError, getUserFriendlyMessage } from "@/types/errors";
import * as Sentry from "@sentry/react-native";

interface Props {
  children: ReactNode;
  fallbackComponent?: React.ComponentType<ChatErrorFallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export interface ChatErrorFallbackProps {
  error: Error | null;
  resetError: () => void;
  retry?: () => void;
}

/**
 * Error boundary specifically designed for chat components
 * Provides better error handling and recovery for chat-related errors
 */
export class ChatErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: number | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Report to crash reporting service
    Sentry.captureException(error, {
      contexts: {
        errorBoundary: {
          componentStack: errorInfo.componentStack,
        },
      },
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Auto-reset for certain types of errors after a delay
    if (this.isAutoRecoverableError(error)) {
      this.resetTimeoutId = window.setTimeout(() => {
        this.resetError();
      }, 5000);
    }
  }

  componentDidUpdate(prevProps: Props) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    if (hasError && resetOnPropsChange) {
      if (resetKeys) {
        const hasResetKeyChanged = resetKeys.some(
          (key, index) => key !== prevProps.resetKeys?.[index]
        );
        if (hasResetKeyChanged) {
          this.resetError();
        }
      }
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  private isAutoRecoverableError = (error: Error): boolean => {
    if (error instanceof ChatError) {
      return error.type === ErrorType.MESSAGE_RENDER_ERROR;
    }
    return false;
  };

  resetError = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallbackComponent || DefaultChatErrorFallback;
      return (
        <FallbackComponent
          error={this.state.error}
          resetError={this.resetError}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Default fallback component for chat errors
 */
function DefaultChatErrorFallback({ error, resetError }: ChatErrorFallbackProps) {
  const { themed } = useAppTheme();
  
  const userMessage = error ? getUserFriendlyMessage(error) : "Something went wrong";
  const isRetryable = error instanceof ChatError ? error.retryable : true;

  return (
    <View style={themed($errorContainer)}>
      <Icon icon="alertCircle" size={32} style={themed($errorIcon)} />
      <Text style={themed($errorTitle)} text="Oops!" />
      <Text style={themed($errorMessage)} text={userMessage} />
      
      {isRetryable && (
        <Button
          style={themed($retryButton)}
          textStyle={themed($retryButtonText)}
          text="Try Again"
          onPress={resetError}
        />
      )}
      
      {__DEV__ && error && (
        <View style={themed($debugContainer)}>
          <Text style={themed($debugText)} text={`Debug: ${error.message}`} />
        </View>
      )}
    </View>
  );
}

const $errorContainer: ThemedStyle<ViewStyle> = (theme) => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  padding: theme.spacing.lg,
  backgroundColor: theme.colors.background,
});

const $errorIcon: ThemedStyle<ViewStyle> = (theme) => ({
  marginBottom: theme.spacing.md,
  tintColor: theme.colors.error,
});

const $errorTitle: ThemedStyle<TextStyle> = (theme) => ({
  fontSize: 18,
  fontWeight: "600",
  color: theme.colors.error,
  marginBottom: theme.spacing.sm,
  textAlign: "center",
});

const $errorMessage: ThemedStyle<TextStyle> = (theme) => ({
  fontSize: 16,
  color: theme.colors.text,
  textAlign: "center",
  marginBottom: theme.spacing.lg,
  lineHeight: 24,
});

const $retryButton: ThemedStyle<ViewStyle> = (theme) => ({
  backgroundColor: theme.colors.palette.primary500,
  paddingHorizontal: theme.spacing.lg,
  paddingVertical: theme.spacing.sm,
  borderRadius: theme.spacing.sm,
});

const $retryButtonText: ThemedStyle<TextStyle> = (theme) => ({
  color: theme.colors.palette.neutral100,
  fontWeight: "600",
});

const $debugContainer: ThemedStyle<ViewStyle> = (theme) => ({
  marginTop: theme.spacing.lg,
  padding: theme.spacing.sm,
  backgroundColor: theme.colors.palette.neutral200,
  borderRadius: theme.spacing.xs,
});

const $debugText: ThemedStyle<TextStyle> = (theme) => ({
  fontSize: 12,
  color: theme.colors.palette.neutral600,
  fontFamily: "monospace",
});

/**
 * Hook to create a chat error boundary with common props
 */
export function useChatErrorBoundary() {
  return {
    ChatErrorBoundary,
    resetKeys: [], // Can be extended to include relevant state keys
  };
}
